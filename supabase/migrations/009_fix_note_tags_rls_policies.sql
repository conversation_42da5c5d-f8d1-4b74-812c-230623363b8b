-- Fix RLS policies for note_tags table
-- This migration ensures proper ownership checks for all operations on note_tags

-- Step 1: Drop the existing INSERT policy that was missing ownership check
DROP POLICY IF EXISTS "Users can create tags for their own notes" ON note_tags;

-- Step 2: Create a new INSERT policy with proper ownership check
CREATE POLICY "Users can create tags for their own notes" ON note_tags
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM notes 
      WHERE notes.id = note_tags.note_id 
      AND notes.user_id = auth.uid()
    )
  );

-- Step 3: Add UPDATE policy for upsert operations
CREATE POLICY "Users can update tags for their own notes" ON note_tags
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM notes 
      WHERE notes.id = note_tags.note_id 
      AND notes.user_id = auth.uid()
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM notes 
      WHERE notes.id = note_tags.note_id 
      AND notes.user_id = auth.uid()
    )
  );

-- Step 4: Add comments to document the policies
COMMENT ON POLICY "Users can create tags for their own notes" ON note_tags IS 
'Allows users to create tags only for notes they own. Uses WITH CHECK to validate ownership during INSERT.';

COMMENT ON POLICY "Users can update tags for their own notes" ON note_tags IS 
'Allows users to update tags only for notes they own. Required for upsert operations in tag management.';

COMMENT ON POLICY "Users can view their own note tags" ON note_tags IS 
'Allows users to view tags only for notes they own.';

COMMENT ON POLICY "Users can delete tags from their own notes" ON note_tags IS 
'Allows users to delete tags only from notes they own.';
