-- Migration: Create update_note_details RPC function for atomic note editing operations
-- This function handles updating note content and managing attachments in a single transaction

CREATE OR REPLACE FUNCTION update_note_details(
  p_note_id UUID,
  p_user_id UUID,
  p_content TEXT,
  p_attachments_to_add TEXT[] DEFAULT '{}',
  p_attachments_to_remove UUID[] DEFAULT '{}'
)
RETURNS TABLE(
  note_id UUID,
  content TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  attachments_count INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_current_attachments_count INTEGER;
  v_final_attachments_count INTEGER;
  v_updated_at TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Check if note exists and user has access
  IF NOT EXISTS (
    SELECT 1 FROM notes 
    WHERE id = p_note_id AND user_id = p_user_id
  ) THEN
    RAISE EXCEPTION 'Note not found or access denied';
  END IF;

  -- Get current attachments count
  SELECT COUNT(*) INTO v_current_attachments_count
  FROM note_attachments 
  WHERE note_attachments.note_id = p_note_id;

  -- Calculate final attachments count after operations
  v_final_attachments_count := v_current_attachments_count 
    - array_length(p_attachments_to_remove, 1) 
    + array_length(p_attachments_to_add, 1);

  -- Validate final attachments count doesn't exceed limit
  IF v_final_attachments_count > 3 THEN
    RAISE EXCEPTION 'Cannot exceed maximum of 3 attachments per note. Current: %, After changes: %', 
      v_current_attachments_count, v_final_attachments_count;
  END IF;

  -- Validate final attachments count is not negative
  IF v_final_attachments_count < 0 THEN
    RAISE EXCEPTION 'Invalid operation: cannot remove more attachments than exist';
  END IF;

  -- Remove specified attachments
  IF array_length(p_attachments_to_remove, 1) > 0 THEN
    DELETE FROM note_attachments 
    WHERE note_attachments.note_id = p_note_id 
      AND id = ANY(p_attachments_to_remove);
    
    -- Verify all specified attachments were removed
    IF FOUND = FALSE AND array_length(p_attachments_to_remove, 1) > 0 THEN
      RAISE EXCEPTION 'Some attachments to remove were not found';
    END IF;
  END IF;

  -- Add new attachments
  IF array_length(p_attachments_to_add, 1) > 0 THEN
    INSERT INTO note_attachments (note_id, url)
    SELECT p_note_id, unnest(p_attachments_to_add);
  END IF;

  -- Update note content and timestamp
  UPDATE notes 
  SET 
    content = p_content,
    updated_at = TIMEZONE('utc', NOW())
  WHERE id = p_note_id AND user_id = p_user_id
  RETURNING notes.updated_at INTO v_updated_at;

  -- Return updated note information
  RETURN QUERY
  SELECT 
    p_note_id as note_id,
    p_content as content,
    v_updated_at as updated_at,
    v_final_attachments_count as attachments_count;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION update_note_details TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION update_note_details IS 
'Atomically updates note content and manages attachments. Validates attachment limits and ensures data consistency.';
