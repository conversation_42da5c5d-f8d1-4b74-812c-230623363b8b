import { NextRequest } from 'next/server'
import { CreateNoteRequest, NotesResponse, CreateNoteResponse } from '@/types/notes'
import { getAllNotesByUserId, createNote, analyzeNoteInBackground } from '@/lib/data/notes'
import { determineContentType } from '@/lib/utils/content-type'
import { requireAuth } from '@/lib/api/auth-middleware'
import { ApiResponses, handleApiError } from '@/lib/api/responses'

export async function GET() {
  try {
    // Check authentication
    const authResult = await requireAuth()
    if (!authResult.success) {
      return authResult.response
    }

    const notes = await getAllNotesByUserId(authResult.user.id)
    return ApiResponses.success({ notes } as NotesResponse)
  } catch (error) {
    return handleApiError(error)
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth()
    if (!authResult.success) {
      return authResult.response
    }

    const { content, content_type, attachment_urls }: CreateNoteRequest = await request.json()

    // Either content or attachment_urls must be provided
    if ((!content || !content.trim()) && (!attachment_urls || attachment_urls.length === 0)) {
      return ApiResponses.badRequest('Content or attachments are required')
    }

    const trimmedContent = content?.trim() || ''

    // Determine content type (explicit or auto-detected)
    const finalContentType = determineContentType(trimmedContent, content_type)

    const note = await createNote(authResult.user.id, trimmedContent, finalContentType, attachment_urls)

    // Trigger AI analysis for text, link content, and notes with attachments
    if (process.env.OPENROUTER_API_KEY && (finalContentType === 'text' || finalContentType === 'link' || (attachment_urls && attachment_urls.length > 0))) {
      // Don't await - run in background
      analyzeNoteInBackground(note.id, authResult.user.id, trimmedContent, finalContentType, attachment_urls)
    }

    return ApiResponses.created({ note } as CreateNoteResponse)
  } catch (error) {
    return handleApiError(error)
  }
}


