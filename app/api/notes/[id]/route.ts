import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { UpdateNoteRequest, UpdateNoteResponse } from '@/types/notes'
import { deleteNote, updateNote } from '@/lib/data/notes'
import { DatabaseError } from '@/lib/data/types'

export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient()

    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: noteId } = await params

    if (!noteId) {
      return NextResponse.json({ error: 'Note ID is required' }, { status: 400 })
    }

    await deleteNote(noteId, user.id)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in DELETE /api/notes/[id]:', error)

    if (error instanceof DatabaseError) {
      if (error.message.includes('not found') || error.message.includes('access denied')) {
        return NextResponse.json({ error: 'Note not found' }, { status: 404 })
      }
      return NextResponse.json({ error: 'Failed to delete note' }, { status: 500 })
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient()

    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: noteId } = await params

    if (!noteId) {
      return NextResponse.json({ error: 'Note ID is required' }, { status: 400 })
    }

    const { content, attachmentsToAdd, attachmentsToRemove }: UpdateNoteRequest = await request.json()

    // Разрешаем пустой контент, если есть изменения в вложениях
    const hasAttachmentChanges = (attachmentsToAdd && attachmentsToAdd.length > 0) ||
                                 (attachmentsToRemove && attachmentsToRemove.length > 0)

    if (!content && !hasAttachmentChanges) {
      return NextResponse.json({ error: 'Content or attachment changes are required' }, { status: 400 })
    }

    const note = await updateNote(
      noteId,
      user.id,
      content?.trim() || '',
      attachmentsToAdd,
      attachmentsToRemove
    )

    // Trigger AI re-analysis if content or attachments changed
    if (attachmentsToAdd?.length || attachmentsToRemove?.length || content) {
      // Import and call background analysis function
      const { analyzeNoteInBackground } = await import('@/lib/data/notes')

      // Get attachment URLs for analysis
      const attachmentUrls = note.attachments?.map(attachment => attachment.url) || []

      analyzeNoteInBackground(
        noteId,
        user.id,
        note.content,
        note.content_type,
        attachmentUrls.length > 0 ? attachmentUrls : undefined
      ).catch(error => {
        console.error('Background AI analysis failed for updated note:', error)
      })
    }

    return NextResponse.json({ note } as UpdateNoteResponse)
  } catch (error) {
    console.error('Error in PUT /api/notes/[id]:', error)

    if (error instanceof DatabaseError) {
      if (error.message.includes('not found') || error.message.includes('access denied')) {
        return NextResponse.json({ error: 'Note not found' }, { status: 404 })
      }
      return NextResponse.json({ error: 'Failed to update note' }, { status: 500 })
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
