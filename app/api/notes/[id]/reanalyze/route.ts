import { NextRequest } from 'next/server'
import { getNoteByIdAndUserId, analyzeNoteInBackground } from '@/lib/data/notes'
import { requireAuth } from '@/lib/api/auth-middleware'
import { ApiResponses, handleApiError } from '@/lib/api/responses'

interface RouteParams {
  params: Promise<{
    id: string
  }>
}

export async function POST(_request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const authResult = await requireAuth()
    if (!authResult.success) {
      return authResult.response
    }

    const user = authResult.user
    const { id: noteId } = await params

    if (!noteId) {
      return ApiResponses.badRequest('ID заметки обязателен')
    }

    // Get the note to verify ownership and get content
    const note = await getNoteByIdAndUserId(noteId, user.id)

    if (!note) {
      return ApiResponses.notFound('Заметка не найдена')
    }

    // Check if OpenRouter API key is available
    if (!process.env.OPENROUTER_API_KEY) {
      return ApiResponses.serviceUnavailable('AI анализ недоступен')
    }

    // Trigger AI analysis in background
    const attachmentUrls = note.attachments?.map(attachment => attachment.url) || []
    const success = await analyzeNoteInBackground(
      noteId,
      note.content,
      note.content_type,
      attachmentUrls.length > 0 ? attachmentUrls : undefined
    )

    if (success) {
      return ApiResponses.success({
        success: true,
        message: 'AI анализ запущен'
      })
    } else {
      return ApiResponses.internalError('Не удалось запустить AI анализ')
    }

  } catch (error) {
    return handleApiError(error)
  }
}
