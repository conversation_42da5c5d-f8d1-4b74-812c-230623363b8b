# Задача 020: Редактирование Вложений в Заметке

## Описание задачи
Реализация функциональности редактирования вложений в существующих заметках. Пользователи должны иметь возможность добавлять новые и удалять существующие изображения при редактировании заметки, с автоматическим обновлением AI-метаданных.

## Анализ текущего состояния

### Существующая архитектура:
- **База данных**: Таблица `note_attachments` с поддержкой до 3-х изображений на заметку
- **Редактирование**: `EditNoteDialog` поддерживает только редактирование текста
- **API**: `PUT /api/notes/[id]` обновляет только поле `content`
- **Загрузка файлов**: `useMultipleFileUpload` и `POST /api/notes/upload-url` для новых заметок
- **AI анализ**: Поддержка множественных изображений с обработкой ошибок

### Проблемы текущего подхода:
1. Невозможность изменения вложений в существующих заметках
2. AI-анализ не обновляется при изменении контента заметки
3. Отсутствует UI для управления вложениями в режиме редактирования

## Цели
- [ ] Расширить `EditNoteDialog` для отображения и управления вложениями
- [ ] Обновить API `PUT /api/notes/[id]` для поддержки изменения вложений
- [ ] Создать RPC-функцию `update_note_details` для атомарных операций
- [ ] Реализовать повторный AI-анализ после изменения контента
- [ ] Обеспечить целостность данных при сбоях

## Детальный план реализации

### Этап 1: Создание RPC-функции для атомарных операций ✅
- [x] Создать функцию `update_note_details` в PostgreSQL
- [x] Параметры: `p_note_id UUID`, `p_user_id UUID`, `p_content TEXT`, `p_attachments_to_add TEXT[]`, `p_attachments_to_remove UUID[]`
- [x] Логика: проверка прав, валидация лимитов, удаление/добавление вложений, обновление заметки
- [x] Транзакционная безопасность

### Этап 2: Обновление API эндпоинта ✅
- [x] Расширить `PUT /api/notes/[id]` для принятия новых полей:
  - `attachmentsToAdd: string[]` - массив URL новых файлов
  - `attachmentsToRemove: string[]` - массив UUID удаляемых файлов
- [x] Интеграция с RPC-функцией
- [x] Запуск повторного AI-анализа после успешного сохранения

### Этап 3: Обновление типов и интерфейсов ✅
- [x] Расширить `UpdateNoteRequest` в `types/notes.ts`
- [x] Обновить функцию `updateNote` в `lib/data/notes.ts`
- [x] Добавить функции для работы с вложениями в DAL

### Этап 4: Расширение EditNoteDialog ✅
- [x] Добавить отображение существующих вложений с возможностью удаления
- [x] Интегрировать `useMultipleFileUpload` для добавления новых файлов
- [x] Реализовать предпросмотр изменений
- [x] Валидация общего количества вложений (не более 3)

### Этап 5: Обновление хука useNotes ✅
- [x] Расширить функцию `updateNote` для поддержки вложений
- [x] Добавить обработку состояний загрузки файлов
- [x] Интеграция с повторным AI-анализом

## Технические детали

### Новая RPC-функция:
```sql
CREATE OR REPLACE FUNCTION update_note_details(
  p_note_id UUID,
  p_user_id UUID,
  p_content TEXT,
  p_attachments_to_add TEXT[] DEFAULT '{}',
  p_attachments_to_remove UUID[] DEFAULT '{}'
)
RETURNS TABLE(
  note_id UUID,
  content TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  attachments_count INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Проверка прав владения заметкой
  -- Валидация итогового количества вложений
  -- DELETE из note_attachments по UUID
  -- INSERT в note_attachments из URL
  -- UPDATE заметки с новым содержимым
  -- Возврат обновленных данных
END;
$$;
```

### Обновленные типы:
```typescript
export interface UpdateNoteRequest {
  content: string
  attachmentsToAdd?: string[]
  attachmentsToRemove?: string[]
}
```

### Новая сигнатура API:
```typescript
// PUT /api/notes/[id]
{
  "content": "string",
  "attachmentsToAdd": ["string"],    // URL новых файлов из Storage
  "attachmentsToRemove": ["uuid"]    // UUID удаляемых файлов
}
```

## Критерии приемки

### Успешное редактирование с добавлением вложений:
- **GIVEN** пользователь редактирует заметку с 1 изображением
- **WHEN** добавляет 2 новых изображения и сохраняет
- **THEN** заметка должна содержать 3 изображения
- **AND** AI-анализ должен обновиться с учетом всех изображений

### Успешное редактирование с удалением вложений:
- **GIVEN** пользователь редактирует заметку с 3 изображениями
- **WHEN** удаляет 2 изображения и сохраняет
- **THEN** заметка должна содержать 1 изображение
- **AND** удаленные файлы должны быть помечены для очистки

### Комбинированное редактирование:
- **GIVEN** пользователь редактирует заметку с 2 изображениями
- **WHEN** удаляет 1 изображение, добавляет 2 новых и меняет текст
- **THEN** все изменения должны быть применены атомарно
- **AND** AI-анализ должен отражать новое содержимое

### Валидация лимитов:
- **GIVEN** пользователь редактирует заметку с 2 изображениями
- **WHEN** пытается добавить 2 новых изображения (итого 4)
- **THEN** должна отображаться ошибка о превышении лимита
- **AND** кнопка добавления должна быть неактивна

### Обработка ошибок:
- **GIVEN** происходит сбой при сохранении в БД
- **WHEN** пользователь пытается сохранить изменения
- **THEN** заметка должна остаться в исходном состоянии
- **AND** пользователь должен увидеть сообщение об ошибке

## Принятые решения (из технической спецификации)

### Запуск AI-анализа:
API-эндпоинт напрямую вызывает фоновую задачу `analyzeNoteInBackground` после успешного сохранения в БД.

### Обработка "осиротевших" файлов:
Будет реализован отдельный фоновый процесс для периодической очистки Storage от неиспользуемых файлов.

### Идентификатор для удаления:
Клиент передает массив **UUID** вложений для точного удаления записей из БД.

## Файлы для реализации

### Новые файлы:
- `supabase/migrations/010_create_update_note_details_function.sql` - RPC-функция для атомарных операций

### Обновляемые файлы:
- `types/notes.ts` - расширение `UpdateNoteRequest`
- `app/api/notes/[id]/route.ts` - поддержка вложений в PUT эндпоинте
- `lib/data/notes.ts` - интеграция с RPC-функцией
- `components/notes/EditNoteDialog.tsx` - UI для управления вложениями
- `hooks/useNotes.ts` - расширение функции `updateNote`

## Реализованные файлы

### Новые файлы:
- `supabase/migrations/010_create_update_note_details_function.sql` - RPC-функция для атомарных операций редактирования заметок

### Обновленные файлы:
- `types/notes.ts` - расширен `UpdateNoteRequest` для поддержки `attachmentsToAdd` и `attachmentsToRemove`
- `app/api/notes/[id]/route.ts` - обновлен PUT эндпоинт для работы с вложениями и запуска AI-анализа
- `lib/data/notes.ts` - расширена функция `updateNote` с интеграцией RPC-функции
- `components/notes/EditNoteDialog.tsx` - полная поддержка управления вложениями в режиме редактирования
- `hooks/useNotes.ts` - обновлена функция `updateNote` для поддержки новых полей
- `app/notes/page.tsx` - обновлена функция `handleEditSave` для передачи параметров вложений

## Ключевые особенности реализации

### 1. Атомарные операции
- RPC-функция `update_note_details` обеспечивает транзакционную безопасность
- Валидация лимитов на уровне базы данных
- Откат всех изменений при ошибках

### 2. Пользовательский интерфейс
- Отображение существующих вложений с возможностью удаления
- Предпросмотр новых файлов перед загрузкой
- Возможность восстановления случайно удаленных вложений
- Динамический счетчик вложений (текущие + новые / максимум)

### 3. Интеграция с AI-анализом
- Автоматический запуск повторного анализа при изменении вложений
- Индикация процесса анализа в UI
- Поддержка множественных изображений в AI-модели

### 4. Обработка ошибок
- Валидация на клиенте и сервере
- Отображение ошибок загрузки файлов
- Graceful fallback при сбоях

## Статус: Полностью реализовано ✅

Функциональность редактирования вложений в заметках полностью реализована согласно технической спецификации. Все этапы завершены, код протестирован на компиляцию. Готово к тестированию и использованию.
