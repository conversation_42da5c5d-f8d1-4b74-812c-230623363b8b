import { createClient } from '@/lib/supabase/server'
import { Note } from '@/types/notes'
import { DatabaseNote, DatabaseError, AiAnalysisStatus } from './types'
import { analyzeNote } from '@/lib/ai/analyze'
import { addTagsToNote } from './tags'
import { createAttachments, saveAttachmentAnalysisError, clearAttachmentAnalysisErrors } from './attachments'
import { prepareContentForAnalysis } from '@/lib/content/preparation'
import type { ContentType } from '@/lib/utils/content-type'
import { AI_ANALYSIS_STATUS_CHECKS } from '@/lib/constants/aiAnalysisStatus'

// Constants for logging
const LOG_MESSAGES = {
  ANALYSIS_START: (noteId: string, contentType: string) => `[AI Analysis] Starting background analysis for note ${noteId} (type: ${contentType})`,
  CONTENT_PREPARED: (length: number) => `[AI Analysis] Content prepared, length: ${length} characters`,
  HAS_ATTACHMENTS: (count: number) => `[AI Analysis] Has attachments: ${count}`,
  SUCCESS: (noteId: string) => `[AI Analysis] Successfully analyzed note ${noteId}`,
  FAILED: (noteId: string) => `[AI Analysis] Failed to analyze note ${noteId}:`,
  TEXT_ANALYSIS_FAILED: '[AI Analysis] Text analysis failed:',
  MULTIMODAL_FAILED: '[AI Analysis] Full multimodal analysis failed, trying individual images:',
  IMAGE_FAILED: (url: string) => `[AI Analysis] Image failed: ${url}`,
  PARTIAL_FAILED: '[AI Analysis] Partial analysis also failed:',
  TEXT_FALLBACK_FAILED: '[AI Analysis] Text-only fallback failed:'
} as const

// Constants for database queries
const NOTE_SELECT_FIELDS = `
  id,
  content,
  content_type,
  summary_ai,
  ai_analysis_status,
  created_at,
  updated_at,
  note_tags (
    tags (
      id,
      name
    )
  ),
  note_attachments (
    id,
    note_id,
    url,
    created_at,
    analysis_error
  )
` as const

// Common error handling
function handleDatabaseError(operation: string, error: any): never {
  console.error(`Database error ${operation}:`, error)
  throw new DatabaseError(`Could not ${operation}`, error)
}

function handleUnexpectedError(operation: string, error: any): never {
  if (error instanceof DatabaseError) {
    throw error
  }
  console.error(`Unexpected error ${operation}:`, error)
  throw new DatabaseError(`Unexpected error occurred while ${operation}`)
}

export async function getAllNotesByUserId(userId: string): Promise<Note[]> {
  try {
    const supabase = await createClient()

    const { data: notes, error } = await supabase
      .from('notes')
      .select(NOTE_SELECT_FIELDS)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      handleDatabaseError('fetch notes', error)
    }

    return formatNotesFromDatabase(notes || [])
  } catch (error) {
    handleUnexpectedError('fetching notes', error)
  }
}

export async function getNoteByIdAndUserId(noteId: string, userId: string): Promise<Note> {
  try {
    const supabase = await createClient()

    const { data: note, error } = await supabase
      .from('notes')
      .select(NOTE_SELECT_FIELDS)
      .eq('id', noteId)
      .eq('user_id', userId)
      .single()

    if (error || !note) {
      throw new DatabaseError('Note not found or access denied')
    }

    return formatNoteFromDatabase(note)
  } catch (error) {
    handleUnexpectedError('fetching note', error)
  }
}

export async function createNote(
  userId: string,
  content: string,
  contentType: 'text' | 'file' | 'link' = 'text',
  attachmentUrls?: string[]
): Promise<Note> {
  try {
    const supabase = await createClient()

    const { data: note, error } = await supabase
      .from('notes')
      .insert({
        user_id: userId,
        content: content?.trim() || '',
        content_type: contentType,
        summary_ai: null,
        ai_analysis_status: 'pending'
      })
      .select()
      .single()

    if (error) {
      console.error('Database error creating note:', error)
      throw new DatabaseError('Could not create note in the database', error)
    }

    // Create attachments if provided
    let attachments: any[] = []
    if (attachmentUrls && attachmentUrls.length > 0) {
      attachments = await createAttachments(note.id, attachmentUrls)
    }

    return formatNoteFromDatabase(note, [], attachments)
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error creating note:', error)
    throw new DatabaseError('Unexpected error while creating note', error)
  }
}

export async function updateNote(
  noteId: string,
  userId: string,
  content: string,
  attachmentsToAdd?: string[],
  attachmentsToRemove?: string[]
): Promise<Note> {
  try {
    const supabase = await createClient()

    // Use RPC function for atomic operations when attachments are involved
    if (attachmentsToAdd?.length || attachmentsToRemove?.length) {
      const { error: rpcError } = await supabase
        .rpc('update_note_details', {
          p_note_id: noteId,
          p_user_id: userId,
          p_content: content.trim(),
          p_attachments_to_add: attachmentsToAdd || [],
          p_attachments_to_remove: attachmentsToRemove || []
        })

      if (rpcError) {
        console.error('RPC error updating note with attachments:', rpcError)
        throw new DatabaseError('Failed to update note with attachments', rpcError)
      }

      // Fetch the complete updated note with all relations
      const { data: updatedNote, error: fetchError } = await supabase
        .from('notes')
        .select(NOTE_SELECT_FIELDS)
        .eq('id', noteId)
        .eq('user_id', userId)
        .single()

      if (fetchError) {
        handleDatabaseError('fetch updated note', fetchError)
      }

      return formatNoteFromDatabase(updatedNote)
    }

    // Simple content-only update for backward compatibility
    const { data: existingNote, error: checkError } = await supabase
      .from('notes')
      .select('id, user_id')
      .eq('id', noteId)
      .eq('user_id', userId)
      .single()

    if (checkError || !existingNote) {
      throw new DatabaseError('Note not found or access denied')
    }

    const { data: updatedNote, error: updateError } = await supabase
      .from('notes')
      .update({
        content: content.trim()
      })
      .eq('id', noteId)
      .eq('user_id', userId)
      .select(NOTE_SELECT_FIELDS)
      .single()

    if (updateError) {
      handleDatabaseError('update note', updateError)
    }

    return formatNoteFromDatabase(updatedNote)
  } catch (error) {
    handleUnexpectedError('updating note', error)
  }
}

export async function deleteNote(noteId: string, userId: string): Promise<void> {
  try {
    const supabase = await createClient()

    const { data: existingNote, error: checkError } = await supabase
      .from('notes')
      .select('id, user_id')
      .eq('id', noteId)
      .eq('user_id', userId)
      .single()

    if (checkError || !existingNote) {
      throw new DatabaseError('Note not found or access denied')
    }

    const { error: deleteError } = await supabase
      .from('notes')
      .delete()
      .eq('id', noteId)
      .eq('user_id', userId)

    if (deleteError) {
      console.error('Database error deleting note:', deleteError)
      throw new DatabaseError('Could not delete note from the database', deleteError)
    }
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error deleting note:', error)
    throw new DatabaseError('Unexpected error while deleting note', error)
  }
}

export async function updateNoteSummary(noteId: string, summary: string): Promise<void> {
  try {
    const supabase = await createClient()

    const { error } = await supabase
      .from('notes')
      .update({ summary_ai: summary })
      .eq('id', noteId)

    if (error) {
      handleDatabaseError('update note summary', error)
    }
  } catch (error) {
    handleUnexpectedError('updating note summary', error)
  }
}

export async function updateNoteAnalysisStatus(noteId: string, status: AiAnalysisStatus): Promise<void> {
  try {
    const supabase = await createClient()

    const { error } = await supabase
      .from('notes')
      .update({ ai_analysis_status: status })
      .eq('id', noteId)

    if (error) {
      handleDatabaseError('update note analysis status', error)
    }
  } catch (error) {
    handleUnexpectedError('updating note analysis status', error)
  }
}

function formatNotesFromDatabase(notes: DatabaseNote[]): Note[] {
  return notes.map(note => formatNoteFromDatabase(note))
}





/**
 * Prepare content for AI analysis based on content type
 */
async function prepareAnalysisContent(content: string, contentType: ContentType): Promise<string> {
  if (contentType === 'link' && content) {
    return await prepareContentForAnalysis(content, contentType)
  }
  return content
}



/**
 * Save analysis results to database
 */
async function saveAnalysisResults(noteId: string, analysis: { tags: string[]; summary: string }): Promise<void> {
  await updateNoteSummary(noteId, analysis.summary)
  await addTagsToNote(noteId, analysis.tags)
}

/**
 * Perform text-only analysis
 */
async function performTextOnlyAnalysis(noteId: string, content: string): Promise<{ success: boolean; hasPartialErrors: boolean }> {
  try {
    const analysis = await analyzeNote(content)
    await saveAnalysisResults(noteId, analysis)
    return { success: true, hasPartialErrors: false }
  } catch (error) {
    console.error(LOG_MESSAGES.TEXT_ANALYSIS_FAILED, error)
    throw error
  }
}

/**
 * Perform AI analysis with error handling for individual images
 */
async function performAiAnalysisWithErrorHandling(
  noteId: string,
  content: string,
  attachmentUrls?: string[]
): Promise<{ success: boolean; hasPartialErrors: boolean }> {
  if (!attachmentUrls || attachmentUrls.length === 0) {
    return await performTextOnlyAnalysis(noteId, content)
  }

  // Try analysis with all images first
  try {
    const analysis = await analyzeNote(content, attachmentUrls)
    await saveAnalysisResults(noteId, analysis)
    return { success: true, hasPartialErrors: false }
  } catch (error) {
    console.log(LOG_MESSAGES.MULTIMODAL_FAILED, error)

    // Try to analyze with valid images only
    const validImageUrls: string[] = []
    const failedImageUrls: string[] = []

    // Test each image URL individually
    for (const imageUrl of attachmentUrls) {
      try {
        // Try a simple analysis with just this image to test if it's valid
        await analyzeNote(content || '[Проверка валидности изображения для AI анализа]', [imageUrl])
        validImageUrls.push(imageUrl)
      } catch (imageError) {
        console.error(LOG_MESSAGES.IMAGE_FAILED(imageUrl), imageError)
        failedImageUrls.push(imageUrl)

        // Save error to database
        await saveAttachmentAnalysisError(noteId, imageUrl, imageError)
      }
    }

    // If we have some valid images, try analysis with them
    if (validImageUrls.length > 0) {
      try {
        const analysis = await analyzeNote(content, validImageUrls)
        await saveAnalysisResults(noteId, analysis)
        return { success: true, hasPartialErrors: failedImageUrls.length > 0 }
      } catch (partialError) {
        console.error(LOG_MESSAGES.PARTIAL_FAILED, partialError)
        throw partialError
      }
    } else {
      // No valid images, try text-only if we have content
      if (content && content.trim()) {
        try {
          const analysis = await analyzeNote(content)
          await saveAnalysisResults(noteId, analysis)
          return { success: true, hasPartialErrors: true }
        } catch (textError) {
          console.error(LOG_MESSAGES.TEXT_FALLBACK_FAILED, textError)
          throw textError
        }
      } else {
        throw new Error('No valid content or images to analyze')
      }
    }
  }
}

export async function analyzeNoteInBackground(
  noteId: string,
  content: string,
  contentType: ContentType = 'text',
  attachmentUrls?: string[]
): Promise<boolean> {
  try {
    console.log(LOG_MESSAGES.ANALYSIS_START(noteId, contentType))

    // Set status to processing
    await updateNoteAnalysisStatus(noteId, 'processing')

    // Prepare content for analysis
    const analysisContent = await prepareAnalysisContent(content, contentType)

    console.log(LOG_MESSAGES.CONTENT_PREPARED(analysisContent.length))
    console.log(LOG_MESSAGES.HAS_ATTACHMENTS(attachmentUrls?.length || 0))

    // Clear any previous analysis errors
    if (attachmentUrls && attachmentUrls.length > 0) {
      await clearAttachmentAnalysisErrors(noteId)
    }

    // Perform AI analysis with error handling
    const result = await performAiAnalysisWithErrorHandling(noteId, analysisContent, attachmentUrls)

    // Set appropriate status based on result
    if (result.hasPartialErrors) {
      await updateNoteAnalysisStatus(noteId, 'partially_completed')
    } else {
      await updateNoteAnalysisStatus(noteId, 'completed')
    }

    console.log(LOG_MESSAGES.SUCCESS(noteId))
    return true
  } catch (error) {
    console.error(LOG_MESSAGES.FAILED(noteId), error)

    // Set status to failed
    await updateNoteAnalysisStatus(noteId, 'failed')

    return false
  }
}

export async function updateNoteWithTags(
  noteId: string,
  userId: string,
  summary: string,
  tags: string[]
): Promise<{ success: boolean; tags: string[]; summary: string }> {
  try {
    const supabase = await createClient()

    const { data: note, error: checkError } = await supabase
      .from('notes')
      .select('id, user_id')
      .eq('id', noteId)
      .eq('user_id', userId)
      .single()

    if (checkError || !note) {
      throw new DatabaseError('Note not found or access denied')
    }

    const { data: result, error: rpcError } = await supabase
      .rpc('update_note_with_tags', {
        p_note_id: noteId,
        p_user_id: userId,
        p_summary: summary,
        p_tags: tags
      })

    if (rpcError) {
      console.error('Database error updating note with tags:', rpcError)
      throw new DatabaseError('Could not update note with tags in the database', rpcError)
    }

    if (!result || !result.success) {
      throw new DatabaseError('Failed to update note with tags')
    }

    return {
      success: true,
      tags: result.tags || tags,
      summary: result.summary || summary
    }
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error
    }
    console.error('Unexpected error updating note with tags:', error)
    throw new DatabaseError('Unexpected error while updating note with tags', error)
  }
}

/**
 * Extract tags from database note structure
 */
function extractTagsFromNote(note: DatabaseNote, overrideTags?: string[]): string[] {
  return overrideTags || note.note_tags?.map((nt: any) => nt.tags.name) || []
}

/**
 * Extract attachments from database note structure
 */
function extractAttachmentsFromNote(note: DatabaseNote, overrideAttachments?: any[]): any[] {
  return overrideAttachments || note.note_attachments?.map((attachment: any) => ({
    id: attachment.id,
    note_id: attachment.note_id || note.id,
    url: attachment.url,
    created_at: attachment.created_at,
    analysis_error: attachment.analysis_error
  })) || []
}

/**
 * Format database note to client format
 */
function formatNoteFromDatabase(note: DatabaseNote, tags?: string[], attachments?: any[]): Note {
  const noteTags = extractTagsFromNote(note, tags)
  const noteAttachments = extractAttachmentsFromNote(note, attachments)

  return {
    id: note.id,
    content: note.content,
    content_type: note.content_type,
    summary_ai: note.summary_ai,
    attachments: noteAttachments,
    created_at: note.created_at,
    updated_at: note.updated_at || note.created_at,
    tags: noteTags,
    aiAnalysisFailed: AI_ANALYSIS_STATUS_CHECKS.hasErrors(note.ai_analysis_status),
    isAnalyzing: AI_ANALYSIS_STATUS_CHECKS.isProcessing(note.ai_analysis_status)
  }
}
