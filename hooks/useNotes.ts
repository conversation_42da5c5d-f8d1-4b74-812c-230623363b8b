import { useState, useEffect } from 'react'
import { Note, CreateNoteRequest, UpdateNoteRequest } from '@/types/notes'

export function useNotes() {
  const [notes, setNotes] = useState<Note[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadNotes = async () => {
    try {
      setError(null)
      const response = await fetch('/api/notes')
      if (response.ok) {
        const data = await response.json()
        setNotes(data.notes)
      } else {
        setError('Не удалось загрузить заметки')
      }
    } catch (err) {
      setError('Ошибка при загрузке заметок')
    }
  }

  const createNote = async (noteData: CreateNoteRequest): Promise<Note | null> => {
    try {
      setError(null)
      const response = await fetch('/api/notes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(noteData),
      })

      if (response.ok) {
        const data = await response.json()
        const newNote = { ...data.note, isAnalyzing: data.note.content_type === 'text' || data.note.content_type === 'link' }
        setNotes(prev => [newNote, ...prev])

        if (data.note.content_type === 'text' || data.note.content_type === 'link') {
          pollForNoteUpdate(newNote.id)
        }

        return newNote
      } else {
        setError('Не удалось создать заметку')
        return null
      }
    } catch (err) {
      setError('Ошибка при создании заметки')
      return null
    }
  }

  const updateNote = async (noteId: string, noteData: UpdateNoteRequest): Promise<Note | null> => {
    try {
      setError(null)
      const response = await fetch(`/api/notes/${noteId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(noteData),
      })

      if (response.ok) {
        const data = await response.json()
        const updatedNote = data.note

        // Mark note as analyzing if content or attachments changed
        const hasContentChanges = noteData.attachmentsToAdd?.length || noteData.attachmentsToRemove?.length
        const noteWithAnalysisState = hasContentChanges
          ? { ...updatedNote, isAnalyzing: true }
          : updatedNote

        setNotes(prev => prev.map(note =>
          note.id === noteId ? noteWithAnalysisState : note
        ))

        // Start polling for AI analysis updates if needed
        if (hasContentChanges) {
          pollForNoteUpdate(noteId)
        }

        return updatedNote
      } else {
        setError('Не удалось обновить заметку')
        return null
      }
    } catch (err) {
      setError('Ошибка при обновлении заметки')
      return null
    }
  }

  const deleteNote = async (noteId: string): Promise<boolean> => {
    try {
      setError(null)
      const response = await fetch(`/api/notes/${noteId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setNotes(prev => prev.filter(note => note.id !== noteId))
        return true
      } else {
        setError('Не удалось удалить заметку')
        return false
      }
    } catch (err) {
      setError('Ошибка при удалении заметки')
      return false
    }
  }

  const pollForNoteUpdate = async (noteId: string) => {
    const maxAttempts = 12
    let attempts = 0

    const poll = async () => {
      if (attempts >= maxAttempts) {
        setNotes(prev => prev.map(note =>
          note.id === noteId ? { ...note, isAnalyzing: false } : note
        ))
        return
      }

      try {
        const response = await fetch(`/api/notes/${noteId}/refresh`)
        if (response.ok) {
          const data = await response.json()
          const updatedNote = data.note

          if (updatedNote.tags.length > 0 || updatedNote.summary_ai) {
            setNotes(prev => prev.map(note =>
              note.id === noteId
                ? { ...updatedNote, isAnalyzing: false }
                : note
            ))
            return
          }
        }
      } catch (error) {
        console.error('Error polling for note update:', error)
      }

      attempts++
      setTimeout(poll, 2000)
    }

    setTimeout(poll, 3000)
  }

  useEffect(() => {
    loadNotes().finally(() => setLoading(false))
  }, [])

  return {
    notes,
    loading,
    error,
    createNote,
    updateNote,
    deleteNote,
    refetch: loadNotes
  }
}
